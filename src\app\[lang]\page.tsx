import { getDictionary } from '@/lib/dictionaries'
import Header from '@/components/Header'
import Hero from '@/components/Hero'
import Features from '@/components/Features'
import About from '@/components/About'
import JoinWaitlist from '@/components/JoinWaitlist'
import Footer from '@/components/Footer'

export default async function Home({
  params,
}: {
  params: Promise<{ lang: 'en' | 'ar' }>
}) {
  const { lang } = await params
  const dict = await getDictionary(lang)

  return (
    <main className="min-h-screen">
      <Header lang={lang} />
      <Hero />
      <Features />
      <About />
      <JoinWaitlist />
      <Footer dict={dict} lang={lang} />
    </main>
  )
}