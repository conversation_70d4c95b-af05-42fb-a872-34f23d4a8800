'use client'

import { useAbout } from '@/hooks/useAbout'
import { useTranslation } from '@/hooks/useTranslation'

export default function About() {
  const { t } = useTranslation()
  const { aboutRef, getTechIcon } = useAbout()



  return (
    <section id="about" ref={aboutRef} className="about-section py-24 relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

        {/* Main About Content */}
        <div className="about-content text-center mb-20">
          <div className="overflow-hidden">
            <h2 className="about-title text-3xl sm:text-4xl font-bold text-black mb-8">
              {t('about.title')}
            </h2>
          </div>
          <div className="overflow-hidden">
            <p className="about-description text-lg text-gray-600 leading-relaxed max-w-4xl mx-auto">
              {t('about.description')}
            </p>
          </div>
        </div>

        {/* Mission Sections - Side by Side */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 mb-24">

          {/* Our Mission */}
          <div className="mission-section">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-black mb-6">
                {t('about.mission.title')}
              </h3>
              <p className="text-gray-700 text-lg leading-relaxed mb-8">
                {t('about.mission.description')}
              </p>
            </div>

            <div className="space-y-6">
              {/* First row - 2 features */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="feature-item bg-white p-6 rounded-xl border border-gray-100 hover:shadow-lg transition-all duration-300">
                  <h4 className="font-semibold text-black text-base mb-1">
                    {t('about.mission.features.unifiedManagement.title')}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {t('about.mission.features.unifiedManagement.description')}
                  </p>
                </div>

                <div className="feature-item bg-white p-6 rounded-xl border border-gray-100 hover:shadow-lg transition-all duration-300">
                  <h4 className="font-semibold text-black text-base mb-1">
                    {t('about.mission.features.aiOptimization.title')}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {t('about.mission.features.aiOptimization.description')}
                  </p>
                </div>
              </div>

              {/* Second row - 1 centered feature */}
              <div className="flex justify-center">
                <div className="feature-item bg-white p-6 rounded-xl border border-gray-100 hover:shadow-lg transition-all duration-300 max-w-md">
                  <h4 className="font-semibold text-black text-base mb-1">
                    {t('about.mission.features.realTimeSync.title')}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {t('about.mission.features.realTimeSync.description')}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Why Choose Rabta */}
          <div className="why-choose-section">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-black mb-6">
                {t('about.whyChoose.title')}
              </h3>
              <p className="text-gray-700 text-lg leading-relaxed mb-8">
                {t('about.whyChoose.description')}
              </p>
            </div>

            <div className="space-y-6">
              {/* First row - 2 features */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="feature-item bg-white p-6 rounded-xl border border-gray-100 hover:shadow-lg transition-all duration-300">
                  <h4 className="font-semibold text-black text-base mb-1">
                    {t('about.whyChoose.features.arabicSupport.title')}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {t('about.whyChoose.features.arabicSupport.description')}
                  </p>
                </div>

                <div className="feature-item bg-white p-6 rounded-xl border border-gray-100 hover:shadow-lg transition-all duration-300">
                  <h4 className="font-semibold text-black text-base mb-1">
                    {t('about.whyChoose.features.localIntegration.title')}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {t('about.whyChoose.features.localIntegration.description')}
                  </p>
                </div>
              </div>

              {/* Second row - 1 centered feature */}
              <div className="flex justify-center">
                <div className="feature-item bg-white p-6 rounded-xl border border-gray-100 hover:shadow-lg transition-all duration-300 max-w-md">
                  <h4 className="font-semibold text-black text-base mb-1">
                    {t('about.whyChoose.features.support24x7.title')}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {t('about.whyChoose.features.support24x7.description')}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>



        {/* Technology Stack */}
        <div className="tech-stack-section text-center">
          <h3 className="text-3xl font-bold text-black mb-8">
            {t('about.technology.title')}
          </h3>
          <p className="text-xl text-gray-600 leading-relaxed max-w-4xl mx-auto mb-12">
            {t('about.technology.description')}
          </p>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
            {[
              { key: 'aiMl', index: 0 },
              { key: 'cloudNative', index: 1 },
              { key: 'realTime', index: 2 },
              { key: 'secure', index: 3 }
            ].map(({ key, index }) => {
              const iconData = getTechIcon(index)
              return (
                <div key={key} className="tech-stack-item text-center">
                  <div className="tech-icon w-16 h-16 bg-gradient-to-br from-black to-gray-700 rounded-2xl flex items-center justify-center mx-auto mb-4 hover:scale-110 transition-transform duration-300">
                    <svg className="w-8 h-8 text-white" fill="currentColor" viewBox={iconData.viewBox}>
                      <path d={iconData.path} />
                    </svg>
                  </div>
                  <h4 className="font-bold text-black mb-2">
                    {t(`about.technology.stack.${key}.title`)}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {t(`about.technology.stack.${key}.description`)}
                  </p>
                </div>
              )
            })}
          </div>
        </div>
      </div>
    </section>
  )
}