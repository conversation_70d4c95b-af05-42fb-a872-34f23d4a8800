'use client'

import { useEffect, useRef, useState } from 'react'
import gsap from 'gsap'

export interface WaitlistFormData {
  email: string
}

export interface DemoFormData {
  name: string
  email: string
  company: string
  phone: string
  message: string
}

export const useHero = () => {
  const heroRef = useRef<HTMLDivElement>(null)
  const iconsRef = useRef<HTMLDivElement>(null)
  
  // Form states
  const [showWaitlistForm, setShowWaitlistForm] = useState(false)
  const [showDemoForm, setShowDemoForm] = useState(false)
  const [showSuccessPopup, setShowSuccessPopup] = useState(false)
  const [successMessage, setSuccessMessage] = useState('')
  const [email, setEmail] = useState('')
  const [demoFormData, setDemoFormData] = useState<DemoFormData>({
    name: '',
    email: '',
    company: '',
    phone: '',
    message: ''
  })

  // Form handlers
  const handleWaitlistSubmit = (e: React.FormEvent, t: (key: string) => string) => {
    e.preventDefault()
    // Handle waitlist submission
    console.log('Waitlist email:', email)
    // Here you would typically send to your backend
    setSuccessMessage(t('hero.waitlist.successMessage'))
    setShowSuccessPopup(true)
    setEmail('')
    setShowWaitlistForm(false)
    // Auto-hide popup after 3 seconds
    setTimeout(() => setShowSuccessPopup(false), 3000)
  }

  const handleDemoSubmit = (e: React.FormEvent, t: (key: string) => string) => {
    e.preventDefault()
    // Handle demo request submission
    console.log('Demo request:', demoFormData)
    // Here you would typically send to your backend
    setSuccessMessage(t('hero.demo.successMessage'))
    setShowSuccessPopup(true)
    setDemoFormData({ name: '', email: '', company: '', phone: '', message: '' })
    setShowDemoForm(false)
    // Auto-hide popup after 3 seconds
    setTimeout(() => setShowSuccessPopup(false), 3000)
  }

  // Animation setup - Start after loading screen
  useEffect(() => {
    const startAnimations = () => {
      const ctx = gsap.context(() => {
        // Cool Rabta title entrance - Letter by letter
        const letters = document.querySelectorAll('.hero-title span')
        gsap.fromTo(letters,
          {
            y: 100,
            opacity: 0,
            rotation: 45,
            scale: 0.5
          },
          {
            y: 0,
            opacity: 1,
            rotation: 0,
            scale: 1,
            duration: 0.8,
            ease: 'back.out(1.7)',
            stagger: 0.1,
            delay: 0.1
          }
        )

        // Underline animation
        gsap.fromTo('.hero-title + div',
          { width: 0 },
          { width: '6rem', duration: 0.8, ease: 'power3.out', delay: 0.8 }
        )

        gsap.fromTo('.hero-subtitle',
          { x: -50, opacity: 0 },
          { x: 0, opacity: 1, duration: 0.8, ease: 'power3.out', delay: 1.0 }
        )

        gsap.fromTo('.hero-description',
          { x: -30, opacity: 0 },
          { x: 0, opacity: 1, duration: 0.8, ease: 'power3.out', delay: 1.2 }
        )

        gsap.fromTo('.hero-cta',
          { y: 30, opacity: 0 },
          { y: 0, opacity: 1, duration: 0.6, ease: 'power3.out', delay: 1.4 }
        )

        gsap.fromTo('.hero-cta-secondary',
          { y: 30, opacity: 0 },
          { y: 0, opacity: 1, duration: 0.6, ease: 'power3.out', delay: 1.5 }
        )

        // Central inventory hub entrance
        gsap.fromTo('.inventory-hub',
          {
            scale: 0,
            rotation: 360,
            opacity: 0
          },
          {
            scale: 1,
            rotation: 0,
            opacity: 1,
            duration: 1,
            ease: 'back.out(1.7)',
            delay: 1.6
          }
        )

        // Platform cards entrance
        gsap.fromTo('.platform-card',
          {
            x: 100,
            opacity: 0,
            scale: 0.8
          },
          {
            x: 0,
            opacity: 1,
            scale: 1,
            duration: 0.8,
            ease: 'back.out(1.7)',
            delay: 1.8,
            stagger: 0.2
          }
        )

      // Connection lines drawing animation with flow
      gsap.fromTo('.connection-line',
        {
          strokeDasharray: '0,1000',
          opacity: 0
        },
        {
          strokeDasharray: '8,8',
          opacity: 1,
          duration: 1.2,
          ease: 'power3.out',
          delay: 2.8,
          stagger: 0.2
        }
      )

      // Connection dots pulsing
      gsap.fromTo('.connection-dot',
        {
          scale: 0,
          opacity: 0
        },
        {
          scale: 1,
          opacity: 1,
          duration: 0.6,
          ease: 'back.out(1.7)',
          delay: 3.2,
          stagger: 0.2
        }
      )

      // AI Features section
      gsap.fromTo('.hero-features',
        { y: 50, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.8, ease: 'power3.out', delay: 2.6 }
      )

      gsap.fromTo('.feature-card',
        { y: 30, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.6, ease: 'power3.out', delay: 2.8, stagger: 0.1 }
      )

      // Subtle floating animation for background elements
      gsap.to('.floating-icon', {
        y: -10,
        duration: 4,
        ease: 'power2.inOut',
        stagger: 0.5,
        repeat: -1,
        yoyo: true
      })

      // Inventory hub pulsing animation
      gsap.to('.inventory-hub', {
        scale: 1.05,
        duration: 2,
        ease: 'power2.inOut',
        repeat: -1,
        yoyo: true,
        delay: 4
      })
 
      // Platform cards floating
      gsap.to('.platform-card', {
        y: -10,
        duration: 3.5,
        ease: 'power2.inOut',
        stagger: 0.5,
        repeat: -1,
        yoyo: true,
        delay: 4
      })

      // Connection dots pulsing continuously
      gsap.to('.connection-dot', {
        scale: 1.3,
        duration: 1.8,
        ease: 'power2.inOut',
        repeat: -1,
        yoyo: true,
        stagger: 0.3,
        delay: 4
      })

      // Animated data flow on connection lines
      gsap.to('.connection-line', {
        strokeDashoffset: -32,
        duration: 3,
        ease: 'none',
        repeat: -1,
        delay: 5
      })

      // Enhanced background particle animations
      gsap.to('.particle-1', {
        x: 40,
        y: -30,
        rotation: 360,
        scale: 1.1,
        duration: 25,
        ease: 'none',
        repeat: -1
      })

      gsap.to('.particle-2', {
        x: -35,
        y: 25,
        rotation: -360,
        scale: 0.9,
        duration: 30,
        ease: 'none',
        repeat: -1
      })

      gsap.to('.particle-3', {
        x: 25,
        y: -15,
        rotation: 180,
        scale: 1.05,
        duration: 20,
        ease: 'none',
        repeat: -1,
        yoyo: true
      })

      gsap.to('.particle-4', {
        x: -20,
        y: 30,
        rotation: -180,
        scale: 0.95,
        duration: 22,
        ease: 'none',
        repeat: -1,
        yoyo: true
      })

      // Medium orb animations
      gsap.to('.orb-1', {
        y: -20,
        x: 15,
        scale: 1.2,
        duration: 8,
        ease: 'power2.inOut',
        repeat: -1,
        yoyo: true
      })

      gsap.to('.orb-2', {
        y: 25,
        x: -10,
        scale: 0.8,
        duration: 10,
        ease: 'power2.inOut',
        repeat: -1,
        yoyo: true
      })

      gsap.to('.orb-3', {
        y: -15,
        x: 20,
        scale: 1.3,
        duration: 6,
        ease: 'power2.inOut',
        repeat: -1,
        yoyo: true
      })

      gsap.to('.orb-4', {
        y: 18,
        x: -12,
        scale: 0.9,
        duration: 9,
        ease: 'power2.inOut',
        repeat: -1,
        yoyo: true
      })

      // Enhanced animated lines
      gsap.to('.animated-line', {
        x: '100vw',
        duration: 12,
        ease: 'none',
        repeat: -1,
        stagger: 3
      })

      // Floating dots animation
      gsap.to('.floating-dot', {
        y: -20,
        scale: 1.5,
        duration: 4,
        ease: 'power2.inOut',
        repeat: -1,
        yoyo: true,
        stagger: 0.5
      })

      // Bigger Dotted Lines Drawing Animation - Top Line
      gsap.fromTo('.connection-line-top line',
        {
          strokeDasharray: '0,1000',
          opacity: 0
        },
        {
          strokeDasharray: '8,8',
          opacity: 1,
          duration: 1.5,
          ease: 'power3.out',
          delay: 2.5
        }
      )

      // Bigger Dotted Lines Drawing Animation - Middle Line
      gsap.fromTo('.connection-line-middle line',
        {
          strokeDasharray: '0,1000',
          opacity: 0
        },
        {
          strokeDasharray: '8,8',
          opacity: 1,
          duration: 1.5,
          ease: 'power3.out',
          delay: 2.8
        }
      )

      // Bigger Dotted Lines Drawing Animation - Bottom Line
      gsap.fromTo('.connection-line-bottom line',
        {
          strokeDasharray: '0,1000',
          opacity: 0
        },
        {
          strokeDasharray: '8,8',
          opacity: 1,
          duration: 1.5,
          ease: 'power3.out',
          delay: 3.1
        }
      )

      // Enhanced Continuous Dotted Line Flow Animation
      gsap.to('.connection-line-top line, .connection-line-middle line, .connection-line-bottom line', {
        strokeDashoffset: -32,
        duration: 3,
        ease: 'none',
        repeat: -1,
        delay: 4.5
      })

      // Enhanced Dotted Lines Glow Effect
      gsap.to('.connection-line-top, .connection-line-middle, .connection-line-bottom', {
        filter: 'drop-shadow(0 0 12px rgba(0, 0, 0, 0.5))',
        duration: 3,
        ease: 'power2.inOut',
        repeat: -1,
        yoyo: true,
        stagger: 0.4,
        delay: 5.5
      })

      // Gentle opacity pulsing only (no scaling/tilting)
      gsap.to('.connection-line-top, .connection-line-middle, .connection-line-bottom', {
        opacity: 0.7,
        duration: 2.5,
        ease: 'power2.inOut',
        repeat: -1,
        yoyo: true,
        stagger: 0.3,
        delay: 6
      })
      }, heroRef)

      return ctx
    }

    // Start animations after loading screen (1.6 seconds)
    const timer = setTimeout(() => {
      startAnimations()
    }, 1600)

    return () => {
      clearTimeout(timer)
    }
  }, [])

  return {
    heroRef,
    iconsRef,
    showWaitlistForm,
    setShowWaitlistForm,
    showDemoForm,
    setShowDemoForm,
    showSuccessPopup,
    setShowSuccessPopup,
    successMessage,
    email,
    setEmail,
    demoFormData,
    setDemoFormData,
    handleWaitlistSubmit,
    handleDemoSubmit
  }
}
