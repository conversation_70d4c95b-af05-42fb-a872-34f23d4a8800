'use client'

import Image from 'next/image'

interface FooterProps {
  dict: {
    footer: {
      copyright: string
      privacy: string
      contact: string
    }
  }
  lang: 'en' | 'ar'
}

export default function Footer({ dict, lang }: FooterProps) {
  return (
    <footer className="bg-gray-50 border-t border-gray-200 py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
          {/* Logo and Description */}
          <div className="md:col-span-2">
            <div className="flex items-center mb-4">
              <Image
                src="/Logo v2.png"
                alt="Rabta Logo"
                width={120}
                height={40}
                className="h-8 w-auto"
              />
            </div>
            <p className="text-gray-600 text-sm leading-relaxed max-w-md">
              {lang === 'en'
                ? "AI-powered multi-channel commerce platform designed specifically for the MENA region. Connect, automate, and grow your business across all digital channels."
                : "منصة التجارة متعددة القنوات المدعومة بالذكاء الاصطناعي المصممة خصيصاً لمنطقة الشرق الأوسط وشمال أفريقيا. اربط وأتمت وانمِ أعمالك عبر جميع القنوات الرقمية."
              }
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-black font-semibold mb-4">
              {lang === 'en' ? 'Quick Links' : 'روابط سريعة'}
            </h4>
            <ul className="space-y-2">
              <li>
                <a href="#features" className="text-gray-600 hover:text-black transition-colors text-sm">
                  {lang === 'en' ? 'Features' : 'المميزات'}
                </a>
              </li>
              <li>
                <a href="#about" className="text-gray-600 hover:text-black transition-colors text-sm">
                  {lang === 'en' ? 'About' : 'حول'}
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-600 hover:text-black transition-colors text-sm">
                  {dict.footer.contact}
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-600 hover:text-black transition-colors text-sm">
                  {dict.footer.privacy}
                </a>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-black font-semibold mb-4">
              {lang === 'en' ? 'Contact' : 'تواصل معنا'}
            </h4>
            <div className="space-y-2">
              <p className="text-gray-600 text-sm">
                {lang === 'en' ? 'Email:' : 'البريد الإلكتروني:'}
              </p>
              <a href="mailto:<EMAIL>" className="text-black hover:text-gray-600 transition-colors text-sm font-medium">
                <EMAIL>
              </a>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-300 pt-8 text-center">
          <p className="text-gray-500 text-sm">{dict.footer.copyright}</p>
        </div>
      </div>
    </footer>
  )
}