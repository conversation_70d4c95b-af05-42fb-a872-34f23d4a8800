'use client'

import { usePathname } from 'next/navigation'
import { useMemo } from 'react'
import enDict from '@/dictionaries/en.json'
import arDict from '@/dictionaries/ar.json'

type Locale = 'en' | 'ar'
type Dictionary = typeof enDict

const dictionaries: Record<Locale, Dictionary> = {
  en: enDict,
  ar: arDict
}

export const useTranslation = () => {
  const pathname = usePathname()
  
  // Extract language from pathname
  const lang: Locale = useMemo(() => {
    const segments = pathname.split('/')
    const firstSegment = segments[1]
    
    if (firstSegment === 'ar' || firstSegment === 'en') {
      return firstSegment as Locale
    }
    
    return 'en' // default fallback
  }, [pathname])

  const dict = dictionaries[lang]

  // Translation function with nested key support
  const t = (key: string): string => {
    const keys = key.split('.')
    let value: unknown = dict

    for (const k of keys) {
      if (value && typeof value === 'object' && value !== null && k in value) {
        value = (value as Record<string, unknown>)[k]
      } else {
        console.warn(`Translation key "${key}" not found for language "${lang}"`)
        return key // Return the key itself if translation is not found
      }
    }

    return typeof value === 'string' ? value : key
  }

  return {
    lang,
    dict,
    t
  }
}
