'use client'

import { useEffect, useState } from 'react'
import { useTranslation } from '@/hooks/useTranslation'

export default function LoadingScreen() {
  const [isLoading, setIsLoading] = useState(true)
  const { lang } = useTranslation()

  useEffect(() => {
    // Show loading for 1.5 seconds then fade out
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 1500)

    return () => clearTimeout(timer)
  }, [])

  if (!isLoading) return null

  return (
    <div className="fixed inset-0 z-50 bg-gradient-to-br from-gray-50 via-white to-blue-50 flex items-center justify-center">
      {/* Loading Animation */}
      <div className="text-center">
        {/* Rabta Logo/Text */}
        <div className="mb-8">
          <h1 className="text-6xl sm:text-7xl lg:text-8xl font-black text-black mb-4 leading-none tracking-tight">
            {lang === 'en' ? (
              <>
                <span className="inline-block animate-bounce" style={{ animationDelay: '0ms' }}>R</span>
                <span className="inline-block animate-bounce" style={{ animationDelay: '100ms' }}>A</span>
                <span className="inline-block animate-bounce" style={{ animationDelay: '200ms' }}>B</span>
                <span className="inline-block animate-bounce" style={{ animationDelay: '300ms' }}>T</span>
                <span className="inline-block animate-bounce" style={{ animationDelay: '400ms' }}>A</span>
              </>
            ) : (
              <>
                <span className="inline-block animate-bounce" style={{ animationDelay: '0ms' }}>ر</span>
                <span className="inline-block animate-bounce" style={{ animationDelay: '100ms' }}>ا</span>
                <span className="inline-block animate-bounce" style={{ animationDelay: '200ms' }}>ب</span>
                <span className="inline-block animate-bounce" style={{ animationDelay: '300ms' }}>ط</span>
                <span className="inline-block animate-bounce" style={{ animationDelay: '400ms' }}>ة</span>
              </>
            )}
          </h1>
        </div>

        {/* Loading Dots */}
        <div className="flex justify-center space-x-2">
          <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse" style={{ animationDelay: '0ms' }}></div>
          <div className="w-3 h-3 bg-purple-500 rounded-full animate-pulse" style={{ animationDelay: '200ms' }}></div>
          <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" style={{ animationDelay: '400ms' }}></div>
        </div>

        {/* Loading Text */}
        <p className="text-gray-600 mt-4 text-lg animate-pulse">
          {lang === 'en' ? 'Loading...' : 'جاري التحميل...'}
        </p>
      </div>

      {/* Background Animation Elements */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute w-64 h-64 bg-gradient-to-r from-blue-200 to-purple-200 rounded-full opacity-20 -top-32 -left-32 animate-spin" style={{ animationDuration: '20s' }}></div>
        <div className="absolute w-48 h-48 bg-gradient-to-r from-green-200 to-blue-200 rounded-full opacity-25 top-1/4 -right-24 animate-spin" style={{ animationDuration: '15s', animationDirection: 'reverse' }}></div>
        <div className="absolute w-32 h-32 bg-gradient-to-r from-purple-200 to-pink-200 rounded-full opacity-30 bottom-1/4 -left-16 animate-spin" style={{ animationDuration: '25s' }}></div>
      </div>
    </div>
  )
}
