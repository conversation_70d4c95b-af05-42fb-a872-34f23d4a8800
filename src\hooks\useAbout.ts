'use client'

import { useEffect, useRef } from 'react'
import gsap from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger)
}

export const useAbout = () => {
  const aboutRef = useRef<HTMLDivElement>(null)

  // Technology stack icons
  const getTechIcon = (index: number) => {
    const iconPaths = [
      // AI & ML
      "M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z",
      // Cloud Native
      "M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z",
      // Real-Time
      "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z",
      // Secure
      "M12 15v2m-6 0v2m3-13a3 3 0 00-3 3v3a3 3 0 106 0V7a3 3 0 00-3-3z"
    ]

    const viewBoxes = ["0 0 20 20", "0 0 20 20", "0 0 20 20", "0 0 20 20"]

    return {
      path: iconPaths[index] || iconPaths[0],
      viewBox: viewBoxes[index] || viewBoxes[0]
    }
  }

  // Animation setup
  useEffect(() => {
    const ctx = gsap.context(() => {
      // About title entrance animation with enhanced effects
      gsap.fromTo('.about-title',
        { 
          y: 60, 
          opacity: 0,
          scale: 0.9
        },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          duration: 1,
          ease: 'back.out(1.7)',
          scrollTrigger: {
            trigger: '.about-section',
            start: 'top 70%',
          }
        }
      )

      // About description entrance animation
      gsap.fromTo('.about-description',
        { 
          y: 40, 
          opacity: 0,
          scale: 0.95
        },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          duration: 0.8,
          delay: 0.3,
          ease: 'power3.out',
          scrollTrigger: {
            trigger: '.about-section',
            start: 'top 70%',
          }
        }
      )

      // Mission section animation with slide effect
      gsap.fromTo('.mission-section',
        { 
          x: -50, 
          opacity: 0,
          rotationY: -15
        },
        {
          x: 0,
          opacity: 1,
          rotationY: 0,
          duration: 1,
          ease: 'power3.out',
          scrollTrigger: {
            trigger: '.mission-section',
            start: 'top 80%',
          }
        }
      )

      // Why choose section animation with slide effect
      gsap.fromTo('.why-choose-section',
        { 
          x: 50, 
          opacity: 0,
          rotationY: 15
        },
        {
          x: 0,
          opacity: 1,
          rotationY: 0,
          duration: 1,
          ease: 'power3.out',
          scrollTrigger: {
            trigger: '.why-choose-section',
            start: 'top 80%',
          }
        }
      )

      // Feature items animation with enhanced stagger
      gsap.fromTo('.feature-item',
        { 
          y: 30, 
          opacity: 0,
          scale: 0.9
        },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          duration: 0.6,
          stagger: 0.1,
          ease: 'back.out(1.7)',
          scrollTrigger: {
            trigger: '.mission-section',
            start: 'top 75%',
          }
        }
      )

      // Technology stack animation
      gsap.fromTo('.tech-stack-item',
        { 
          y: 30, 
          opacity: 0,
          scale: 0.8,
          rotation: 10
        },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          rotation: 0,
          duration: 0.6,
          stagger: 0.1,
          ease: 'back.out(1.7)',
          scrollTrigger: {
            trigger: '.tech-stack-section',
            start: 'top 80%',
          }
        }
      )

      // Tech icons floating animation
      gsap.to('.tech-icon', {
        y: -8,
        duration: 2,
        ease: 'power2.inOut',
        repeat: -1,
        yoyo: true,
        stagger: 0.2,
        delay: 1
      })

      // Feature items hover enhancement
      const featureItems = document.querySelectorAll('.feature-item')
      featureItems.forEach((item) => {
        item.addEventListener('mouseenter', () => {
          gsap.to(item, {
            scale: 1.05,
            duration: 0.3,
            ease: 'power2.out'
          })
        })

        item.addEventListener('mouseleave', () => {
          gsap.to(item, {
            scale: 1,
            duration: 0.3,
            ease: 'power2.out'
          })
        })
      })

      // Tech stack items hover enhancement
      const techItems = document.querySelectorAll('.tech-stack-item')
      techItems.forEach((item) => {
        item.addEventListener('mouseenter', () => {
          gsap.to(item, {
            scale: 1.1,
            y: -5,
            duration: 0.3,
            ease: 'power2.out'
          })
        })

        item.addEventListener('mouseleave', () => {
          gsap.to(item, {
            scale: 1,
            y: 0,
            duration: 0.3,
            ease: 'power2.out'
          })
        })
      })

    }, aboutRef)

    return () => ctx.revert()
  }, [])

  return {
    aboutRef,
    getTechIcon
  }
}
