'use client'

import { useFeatures } from '@/hooks/useFeatures'
import { useTranslation } from '@/hooks/useTranslation'

export default function Features() {
  const { t } = useTranslation()
  const { featuresRef, getFeatureIcon } = useFeatures()

  const features = [
    {
      title: t('features.multiChannel.title'),
      description: t('features.multiChannel.description'),
      iconData: getFeatureIcon(0)
    },
    {
      title: t('features.menaFocused.title'),
      description: t('features.menaFocused.description'),
      iconData: getFeatureIcon(1)
    },
    {
      title: t('features.analytics.title'),
      description: t('features.analytics.description'),
      iconData: getFeatureIcon(2)
    },
    {
      title: t('features.automation.title'),
      description: t('features.automation.description'),
      iconData: getFeatureIcon(3)
    }
  ]
  return (
    <section ref={featuresRef} className="features-section py-20 relative">
      {/* Background elements for animation */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="feature-bg-element absolute top-20 left-10 w-32 h-32 bg-blue-100 rounded-full opacity-20"></div>
        <div className="feature-bg-element absolute bottom-20 right-10 w-24 h-24 bg-purple-100 rounded-full opacity-20"></div>
        <div className="feature-bg-element absolute top-1/2 left-1/2 w-16 h-16 bg-pink-100 rounded-full opacity-20"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="feature-title text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            {t('features.title')}
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t('features.subtitle')}
          </p>
        </div>

        <div className="features-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="feature-card bg-white rounded-2xl p-8 text-center hover:shadow-xl transition-all duration-300 border border-gray-100 group">
              <div className="feature-icon inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 text-white rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-8 h-8" fill="currentColor" viewBox={feature.iconData.viewBox}>
                  <path d={feature.iconData.path} />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                {feature.title}
              </h3>
              <p className="text-gray-600 leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}