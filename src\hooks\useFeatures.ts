'use client'

import { useEffect, useRef } from 'react'
import gsap from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger)
}

export const useFeatures = () => {
  const featuresRef = useRef<HTMLDivElement>(null)

  // Feature icons component data
  const getFeatureIcon = (index: number) => {
    const iconPaths = [
      // Multi-channel
      "M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z",
      // MENA-focused
      "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z",
      // Analytics
      "M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z",
      // Automation
      "M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
    ]

    const viewBoxes = ["0 0 24 24", "0 0 24 24", "0 0 24 24", "0 0 24 24"]

    return {
      path: iconPaths[index] || iconPaths[0],
      viewBox: viewBoxes[index] || viewBoxes[0]
    }
  }

  // Animation setup
  useEffect(() => {
    const ctx = gsap.context(() => {
      // Title entrance animation
      gsap.fromTo('.feature-title', 
        { 
          y: 50, 
          opacity: 0,
          scale: 0.9
        },
        { 
          y: 0, 
          opacity: 1,
          scale: 1,
          duration: 1,
          ease: 'back.out(1.7)',
          scrollTrigger: {
            trigger: '.features-section',
            start: 'top 80%',
          }
        }
      )

      // Feature cards entrance animation
      gsap.fromTo('.feature-card', 
        { 
          y: 80, 
          opacity: 0,
          scale: 0.8,
          rotation: 5
        },
        { 
          y: 0, 
          opacity: 1,
          scale: 1,
          rotation: 0,
          duration: 0.8,
          ease: 'back.out(1.7)',
          stagger: 0.2,
          scrollTrigger: {
            trigger: '.features-grid',
            start: 'top 80%',
          }
        }
      )

      // Feature icons floating animation
      gsap.to('.feature-icon', {
        y: -10,
        duration: 2,
        ease: 'power2.inOut',
        repeat: -1,
        yoyo: true,
        stagger: 0.3,
        delay: 1
      })

      // Feature cards hover effect enhancement
      const featureCards = document.querySelectorAll('.feature-card')
      featureCards.forEach((card) => {
        card.addEventListener('mouseenter', () => {
          gsap.to(card, {
            scale: 1.05,
            y: -10,
            duration: 0.3,
            ease: 'power2.out'
          })
        })

        card.addEventListener('mouseleave', () => {
          gsap.to(card, {
            scale: 1,
            y: 0,
            duration: 0.3,
            ease: 'power2.out'
          })
        })
      })

      // Background elements animation
      gsap.to('.feature-bg-element', {
        rotation: 360,
        duration: 20,
        ease: 'none',
        repeat: -1,
        stagger: 2
      })

    }, featuresRef)

    return () => ctx.revert()
  }, [])

  return {
    featuresRef,
    getFeatureIcon
  }
}
