'use client'

import { useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import Image from 'next/image'
import gsap from 'gsap'

interface HeaderProps {
  lang: 'en' | 'ar'
}

export default function Header({ lang }: HeaderProps) {
  const [isScrolled, setIsScrolled] = useState(false)
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  useEffect(() => {
    gsap.fromTo('.header-content', 
      { y: -100, opacity: 0 },
      { y: 0, opacity: 1, duration: 1, ease: 'power3.out' }
    )
  }, [])

  const switchLanguage = (newLang: 'en' | 'ar') => {
    const newPath = pathname.replace(`/${lang}`, `/${newLang}`)
    document.cookie = `NEXT_LOCALE=${newLang}; path=/; max-age=31536000`
    router.push(newPath)
  }

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      isScrolled ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200' : 'bg-transparent'
    }`}>
      <div className="header-content max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex-shrink-0 flex items-center">
            <Image
              src={lang === 'ar' ? '/logo_ar.png' : '/Logo.png'}
              alt="Rabta Logo"
              width={180}
              height={60}
              className="h-16 w-auto"
              priority
            />
          </div>

          {/* Navigation Links */}
          <div className="hidden md:flex items-center space-x-8">
            <a
              href="#home"
              className="text-gray-700 hover:text-black font-medium transition-colors"
            >
              {lang === 'en' ? 'Home' : 'الرئيسية'}
            </a>
            <a
              href="#about"
              className="text-gray-700 hover:text-black font-medium transition-colors"
            >
              {lang === 'en' ? 'About' : 'حول'}
            </a>
            <a
              href="#features"
              className="text-gray-700 hover:text-black font-medium transition-colors"
            >
              {lang === 'en' ? 'Features' : 'المميزات'}
            </a>
            <a
              href="#contact"
              className="text-gray-700 hover:text-black font-medium transition-colors"
            >
              {lang === 'en' ? 'Contact' : 'تواصل'}
            </a>
          </div>

          <div className="flex items-center space-x-4">
            <div className="flex items-center bg-gray-100 rounded-full p-1 border border-gray-200">
              <button
                onClick={() => switchLanguage('en')}
                className={`px-3 py-1 rounded-full text-sm font-semibold transition-all ${
                  lang === 'en'
                    ? 'bg-black text-white'
                    : 'text-gray-600 hover:text-black'
                }`}
              >
                EN
              </button>
              <button
                onClick={() => switchLanguage('ar')}
                className={`px-3 py-1 rounded-full text-sm font-semibold transition-all ${
                  lang === 'ar'
                    ? 'bg-black text-white'
                    : 'text-gray-600 hover:text-black'
                }`}
              >
                AR
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}