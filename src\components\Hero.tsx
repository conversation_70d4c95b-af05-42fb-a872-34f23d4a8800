'use client'

import Image from 'next/image'
import { useHero } from '@/hooks/useHero'
import { useTranslation } from '@/hooks/useTranslation'

export default function Hero() {
  const { lang, t } = useTranslation()
  const {
    heroRef,
    iconsRef,
    showWaitlistForm,
    setShowWaitlistForm,
    showDemoForm,
    setShowDemoForm,
    showSuccessPopup,
    setShowSuccessPopup,
    successMessage,
    email,
    setEmail,
    demoFormData,
    setDemoFormData,
    handleWaitlistSubmit,
    handleDemoSubmit
  } = useHero()

  // Wrapper functions to pass translation function to handlers
  const onWaitlistSubmit = (e: React.FormEvent) => handleWaitlistSubmit(e, t)
  const onDemoSubmit = (e: React.FormEvent) => handleDemoSubmit(e, t)

  return (
    <section id="home" ref={heroRef} className="relative bg-gradient-to-br from-gray-50 via-white to-blue-50 overflow-hidden">
      {/* Enhanced Animated Background Elements */}
      <div ref={iconsRef} className="absolute inset-0 pointer-events-none">
        {/* Large floating particles with better visibility */}
        <div className="particle-1 absolute w-80 h-80 bg-gradient-to-r from-blue-200 to-purple-200 rounded-full opacity-30 -top-40 -left-40 blur-3xl"></div>
        <div className="particle-2 absolute w-64 h-64 bg-gradient-to-r from-green-200 to-blue-200 rounded-full opacity-35 top-1/4 -right-32 blur-2xl"></div>
        <div className="particle-3 absolute w-48 h-48 bg-gradient-to-r from-purple-200 to-pink-200 rounded-full opacity-40 bottom-1/4 -left-24 blur-xl"></div>
        <div className="particle-4 absolute w-56 h-56 bg-gradient-to-r from-yellow-200 to-orange-200 rounded-full opacity-30 -bottom-28 -right-28 blur-2xl"></div>

        {/* Medium floating orbs */}
        <div className="orb-1 absolute w-24 h-24 bg-gradient-to-r from-indigo-300 to-blue-300 rounded-full opacity-25 top-16 right-1/4 blur-lg"></div>
        <div className="orb-2 absolute w-32 h-32 bg-gradient-to-r from-teal-300 to-green-300 rounded-full opacity-20 bottom-16 left-1/3 blur-lg"></div>
        <div className="orb-3 absolute w-20 h-20 bg-gradient-to-r from-rose-300 to-pink-300 rounded-full opacity-30 top-1/2 left-16 blur-md"></div>
        <div className="orb-4 absolute w-28 h-28 bg-gradient-to-r from-amber-300 to-yellow-300 rounded-full opacity-25 top-3/4 right-16 blur-lg"></div>

        {/* Floating tech icons with better visibility */}
        <div className="floating-icon absolute top-20 left-10 w-10 h-10 text-gray-400 opacity-60">
          <svg fill="currentColor" viewBox="0 0 20 20">
            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
          </svg>
        </div>
        <div className="floating-icon absolute top-40 right-20 w-8 h-8 text-gray-400 opacity-50">
          <svg fill="currentColor" viewBox="0 0 20 20">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
        </div>
        <div className="floating-icon absolute bottom-32 left-20 w-12 h-12 text-gray-400 opacity-70">
          <svg fill="currentColor" viewBox="0 0 20 20">
            <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"/>
          </svg>
        </div>
        <div className="floating-icon absolute bottom-20 right-10 w-9 h-9 text-gray-400 opacity-55">
          <svg fill="currentColor" viewBox="0 0 20 20">
            <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"/>
          </svg>
        </div>
        <div className="floating-icon absolute top-1/3 left-1/4 w-7 h-7 text-gray-400 opacity-65">
          <svg fill="currentColor" viewBox="0 0 20 20">
            <path d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
        </div>
        <div className="floating-icon absolute top-2/3 right-1/3 w-8 h-8 text-gray-400 opacity-60">
          <svg fill="currentColor" viewBox="0 0 20 20">
            <path d="M4 3a2 2 0 100 4h12a2 2 0 100-4H4z"/>
            <path fillRule="evenodd" d="M3 8h14v7a2 2 0 01-2 2H5a2 2 0 01-2-2V8zm5 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" clipRule="evenodd"/>
          </svg>
        </div>

        {/* Enhanced animated lines */}
        <div className="animated-line absolute top-1/4 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-blue-300 to-transparent opacity-40"></div>
        <div className="animated-line absolute top-3/4 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-purple-300 to-transparent opacity-40"></div>
        <div className="animated-line absolute top-1/2 left-0 w-full h-px bg-gradient-to-r from-transparent via-green-300 to-transparent opacity-30"></div>

        {/* Floating dots */}
        <div className="floating-dot absolute top-24 right-1/3 w-3 h-3 bg-blue-400 rounded-full opacity-50"></div>
        <div className="floating-dot absolute bottom-24 left-1/4 w-2 h-2 bg-purple-400 rounded-full opacity-60"></div>
        <div className="floating-dot absolute top-1/2 right-20 w-4 h-4 bg-green-400 rounded-full opacity-40"></div>
        <div className="floating-dot absolute bottom-1/3 left-10 w-2.5 h-2.5 bg-pink-400 rounded-full opacity-55"></div>
      </div>

      {/* Main Content Container */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center min-h-screen">

          {/* Left Column - Content */}
          <div className="lg:order-1">
            {/* Cool Rabta Title */}
            <div className="mb-8">
              <h1 className="hero-title text-7xl sm:text-8xl lg:text-9xl font-black text-black mb-4 leading-none tracking-tight">
                {lang === 'en' ? (
                  <>
                    <span className="inline-block transform hover:scale-105 transition-transform duration-300">R</span>
                    <span className="inline-block transform hover:scale-105 transition-transform duration-300 delay-75">A</span>
                    <span className="inline-block transform hover:scale-105 transition-transform duration-300 delay-150">B</span>
                    <span className="inline-block transform hover:scale-105 transition-transform duration-300 delay-225">T</span>
                    <span className="inline-block transform hover:scale-105 transition-transform duration-300 delay-300">A</span>
                  </>
                ) : (
                  <span className="inline-block transform hover:scale-105 transition-transform duration-300">رابطة</span>
                )}
              </h1>
              <div className="w-24 h-1 bg-black mb-6"></div>
            </div>

            <h2 className="hero-subtitle text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-800 mb-6 leading-tight">
              {t('hero.title')}
            </h2>

            <p className="hero-description text-lg sm:text-xl text-gray-600 mb-8 leading-relaxed max-w-lg">
              {t('hero.subtitle')}
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 mb-12">
              <button
                onClick={() => setShowWaitlistForm(true)}
                className="hero-cta bg-black hover:bg-gray-800 text-white font-semibold py-4 px-8 rounded-lg text-lg transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                {t('hero.cta')}
              </button>
              <button
                onClick={() => setShowDemoForm(true)}
                className="hero-cta-secondary border-2 border-black text-black hover:bg-black hover:text-white font-semibold py-4 px-8 rounded-lg text-lg transition-all duration-300"
              >
                {t('hero.demoButton')}
              </button>
            </div>


          </div>

          {/* Right Column - 3-Column Layout: Inventory, Arrows, Services */}
          <div className="lg:order-2 flex items-center justify-center py-20">
            <div className="grid grid-cols-3 gap-0 lg:gap-1 xl:gap-2 max-w-3xl w-full items-center">

              {/* Column 1: Inventory (always first column) */}
              <div className="order-1 flex justify-center">
                <div className="relative">
                  <div className="inventory-hub w-20 h-20 sm:w-24 sm:h-24 md:w-28 md:h-28 lg:w-36 lg:h-36 xl:w-44 xl:h-44 bg-black rounded-3xl flex flex-col items-center justify-center shadow-2xl">
                    <svg className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 lg:w-14 lg:h-14 xl:w-18 xl:h-18 text-white mb-1 lg:mb-3" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M4 3a2 2 0 100 4h12a2 2 0 100-4H4z"/>
                      <path fillRule="evenodd" d="M3 8h14v7a2 2 0 01-2 2H5a2 2 0 01-2-2V8zm5 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" clipRule="evenodd"/>
                    </svg>
                  </div>
                </div>
              </div>

              {/* Column 2: Animated Dotted Lines */}
              <div className="order-2 flex flex-col items-center justify-center space-y-8 lg:space-y-12 relative">

                {/* Top Dotted Line - Tilted Up for English, Down for Arabic */}
                <div className="relative w-16 lg:w-24 h-2">
                  <svg className={`connection-line-top absolute inset-0 w-full h-full transform ${lang === 'ar' ? 'rotate-45' : '-rotate-45'}`} viewBox="0 0 100 10">
                    <line
                      x1="0" y1="5" x2="100" y2="5"
                      stroke="currentColor"
                      strokeWidth="4"
                      strokeDasharray="8,8"
                      className="text-black"
                    />
                  </svg>
                </div>

                {/* Middle Dotted Line - Straight */}
                <div className="relative w-16 lg:w-24 h-2">
                  <svg className="connection-line-middle absolute inset-0 w-full h-full" viewBox="0 0 100 10">
                    <line
                      x1="0" y1="5" x2="100" y2="5"
                      stroke="currentColor"
                      strokeWidth="4"
                      strokeDasharray="8,8"
                      className="text-black"
                    />
                  </svg>
                </div>

                {/* Bottom Dotted Line - Tilted Down for English, Up for Arabic */}
                <div className="relative w-16 lg:w-24 h-2">
                  <svg className={`connection-line-bottom absolute inset-0 w-full h-full transform ${lang === 'ar' ? '-rotate-45' : 'rotate-45'}`} viewBox="0 0 100 10">
                    <line
                      x1="0" y1="5" x2="100" y2="5"
                      stroke="currentColor"
                      strokeWidth="4"
                      strokeDasharray="8,8"
                      className="text-black"
                    />
                  </svg>
                </div>

              </div>

              {/* Column 3: Services (always third column) */}
              <div className="order-3 flex justify-center">
                <div className="grid grid-cols-1 gap-8 lg:gap-12">
                  {/* Amazon - Much Bigger */}
                  <div className="platform-card group relative">
                    <div className="w-56 h-20 sm:w-64 sm:h-24 lg:w-96 lg:h-40 flex items-center justify-center transition-all duration-500 group-hover:scale-105">
                      <Image
                        src="/assets/landing/amazon_logo.png"
                        alt="Amazon"
                        width={220}
                        height={110}
                        className="h-14 sm:h-16 lg:h-28 w-auto"
                      />
                    </div>
                  </div>

                  {/* Noon - A little bigger */}
                  <div className="platform-card group relative">
                    <div className="w-56 h-20 sm:w-64 sm:h-24 lg:w-96 lg:h-40 flex items-center justify-center transition-all duration-500 group-hover:scale-105">
                      <Image
                        src="/assets/landing/noon_logo.png"
                        alt="Noon"
                        width={140}
                        height={70}
                        className="h-10 sm:h-12 lg:h-18 w-auto"
                      />
                    </div>
                  </div>

                  {/* Shopify - Much Bigger */}
                  <div className="platform-card group relative">
                    <div className="w-56 h-20 sm:w-64 sm:h-24 lg:w-96 lg:h-40 flex items-center justify-center transition-all duration-500 group-hover:scale-105">
                      <Image
                        src="/assets/landing/shopify_logo.png"
                        alt="Shopify"
                        width={220}
                        height={110}
                        className="h-14 sm:h-16 lg:h-28 w-auto"
                      />
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>

        {/* AI Features Section */}
        <div id="features" className="hero-features mt-20 pt-20 border-t border-gray-200">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-black mb-4">
              {t('hero.featuresTitle')}
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              {t('hero.featuresSubtitle')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="feature-card text-center p-8 bg-white rounded-xl border border-gray-200 hover:border-black hover:shadow-lg transition-all duration-300">
              <div className="w-16 h-16 bg-black rounded-xl flex items-center justify-center mb-6 mx-auto">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <h3 className="font-bold text-black text-xl mb-3">
                {t('hero.features.smartAnalytics.title')}
              </h3>
              <p className="text-gray-600">
                {t('hero.features.smartAnalytics.description')}
              </p>
            </div>

            <div className="feature-card text-center p-8 bg-white rounded-xl border border-gray-200 hover:border-black hover:shadow-lg transition-all duration-300">
              <div className="w-16 h-16 bg-black rounded-xl flex items-center justify-center mb-6 mx-auto">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <h3 className="font-bold text-black text-xl mb-3">
                {t('hero.features.smartPricing.title')}
              </h3>
              <p className="text-gray-600">
                {t('hero.features.smartPricing.description')}
              </p>
            </div>

            <div className="feature-card text-center p-8 bg-white rounded-xl border border-gray-200 hover:border-black hover:shadow-lg transition-all duration-300">
              <div className="w-16 h-16 bg-black rounded-xl flex items-center justify-center mb-6 mx-auto">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                </svg>
              </div>
              <h3 className="font-bold text-black text-xl mb-3">
                {t('hero.features.automatedWorkflows.title')}
              </h3>
              <p className="text-gray-600">
                {t('hero.features.automatedWorkflows.description')}
              </p>
            </div>

            <div className="feature-card text-center p-8 bg-white rounded-xl border border-gray-200 hover:border-black hover:shadow-lg transition-all duration-300">
              <div className="w-16 h-16 bg-black rounded-xl flex items-center justify-center mb-6 mx-auto">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"/>
                </svg>
              </div>
              <h3 className="font-bold text-black text-xl mb-3">
                {t('hero.features.realTimeSync.title')}
              </h3>
              <p className="text-gray-600">
                {t('hero.features.realTimeSync.description')}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Waitlist Form Modal */}
      {showWaitlistForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl p-8 max-w-md w-full">
            <div className="text-center mb-6">
              <h3 className="text-2xl font-bold text-black mb-2">
                {t('hero.waitlist.title')}
              </h3>
              <p className="text-gray-600">
                {t('hero.waitlist.subtitle')}
              </p>
            </div>
            <form onSubmit={onWaitlistSubmit}>
              <div className="mb-4">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder={t('hero.waitlist.emailPlaceholder')}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-black bg-gray-50 text-gray-900 placeholder-gray-500"
                  required
                />
              </div>
              <div className="flex gap-3">
                <button
                  type="submit"
                  className="flex-1 bg-black text-white py-3 rounded-lg font-semibold hover:bg-gray-800 transition-colors"
                >
                  {t('hero.waitlist.submitButton')}
                </button>
                <button
                  type="button"
                  onClick={() => setShowWaitlistForm(false)}
                  className="flex-1 border border-gray-300 text-gray-700 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors"
                >
                  {t('hero.waitlist.cancelButton')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Demo Request Form Modal */}
      {showDemoForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl p-8 max-w-lg w-full max-h-[90vh] overflow-y-auto">
            <div className="text-center mb-6">
              <h3 className="text-2xl font-bold text-black mb-2">
                {t('hero.demo.title')}
              </h3>
              <p className="text-gray-600">
                {t('hero.demo.subtitle')}
              </p>
            </div>
            <form onSubmit={onDemoSubmit}>
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    {t('hero.demo.nameLabel')}
                  </label>
                  <input
                    type="text"
                    value={demoFormData.name}
                    onChange={(e) => setDemoFormData({...demoFormData, name: e.target.value})}
                    placeholder={t('hero.demo.namePlaceholder')}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-black focus:ring-1 focus:ring-black bg-gray-50 text-gray-900 placeholder-gray-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    {t('hero.demo.emailLabel')}
                  </label>
                  <input
                    type="email"
                    value={demoFormData.email}
                    onChange={(e) => setDemoFormData({...demoFormData, email: e.target.value})}
                    placeholder={t('hero.demo.emailPlaceholder')}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-black focus:ring-1 focus:ring-black bg-gray-50 text-gray-900 placeholder-gray-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    {t('hero.demo.companyLabel')}
                  </label>
                  <input
                    type="text"
                    value={demoFormData.company}
                    onChange={(e) => setDemoFormData({...demoFormData, company: e.target.value})}
                    placeholder={t('hero.demo.companyPlaceholder')}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-black focus:ring-1 focus:ring-black bg-gray-50 text-gray-900 placeholder-gray-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    {t('hero.demo.phoneLabel')}
                  </label>
                  <input
                    type="tel"
                    value={demoFormData.phone}
                    onChange={(e) => setDemoFormData({...demoFormData, phone: e.target.value})}
                    placeholder={t('hero.demo.phonePlaceholder')}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-black focus:ring-1 focus:ring-black bg-gray-50 text-gray-900 placeholder-gray-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    {t('hero.demo.messageLabel')}
                  </label>
                  <textarea
                    value={demoFormData.message}
                    onChange={(e) => setDemoFormData({...demoFormData, message: e.target.value})}
                    placeholder={t('hero.demo.messagePlaceholder')}
                    rows={4}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-black focus:ring-1 focus:ring-black resize-none bg-gray-50 text-gray-900 placeholder-gray-500"
                  />
                </div>
              </div>
              <div className="flex gap-3 mt-6">
                <button
                  type="submit"
                  className="flex-1 bg-black text-white py-3 rounded-lg font-semibold hover:bg-gray-800 transition-colors"
                >
                  {t('hero.demo.submitButton')}
                </button>
                <button
                  type="button"
                  onClick={() => setShowDemoForm(false)}
                  className="flex-1 border border-gray-300 text-gray-700 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors"
                >
                  {t('hero.demo.cancelButton')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Success Popup */}
      {showSuccessPopup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl p-8 max-w-md mx-4 text-center shadow-2xl transform animate-bounce">
            <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
              </svg>
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-2">Success!</h3>
            <p className="text-gray-600 mb-6">{successMessage}</p>
            <button
              onClick={() => setShowSuccessPopup(false)}
              className="bg-black text-white px-6 py-2 rounded-lg hover:bg-gray-800 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      )}
    </section>
  )
}