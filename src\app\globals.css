@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
}

/* Enhanced Bigger Dotted Lines Animations */
@keyframes bigDottedFlow {
  0% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: -32;
  }
}

@keyframes enhancedLineGlow {
  0%, 100% {
    filter: drop-shadow(0 0 6px rgba(0, 0, 0, 0.4));
    opacity: 0.8;
  }
  50% {
    filter: drop-shadow(0 0 16px rgba(0, 0, 0, 0.7));
    opacity: 1;
  }
}

@keyframes lineShimmer {
  0% {
    stroke: #000;
  }
  50% {
    stroke: #333;
  }
  100% {
    stroke: #000;
  }
}

.connection-line-top,
.connection-line-middle,
.connection-line-bottom {
  animation: enhancedLineGlow 4s ease-in-out infinite;
  transition: all 0.4s ease;
  filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.3));
}

.connection-line-top line,
.connection-line-middle line,
.connection-line-bottom line {
  animation: bigDottedFlow 3s linear infinite, lineShimmer 2s ease-in-out infinite;
  stroke-linecap: round;
}

/* Staggered animation delays for visual appeal */
.connection-line-middle {
  animation-delay: 0.3s, 0.2s;
}

.connection-line-bottom {
  animation-delay: 0.6s, 0.4s;
}

/* Enhanced Hover Effects */
.connection-line-top:hover,
.connection-line-middle:hover,
.connection-line-bottom:hover {
  filter: drop-shadow(0 0 20px rgba(0, 0, 0, 0.9));
  transform: scale(1.1);
}

.connection-line-top:hover line,
.connection-line-middle:hover line,
.connection-line-bottom:hover line {
  stroke-width: 6;
  animation-duration: 1.5s;
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .space-x-4 > * + * {
  margin-left: 0;
  margin-right: 1rem;
}

[dir="rtl"] .space-x-6 > * + * {
  margin-left: 0;
  margin-right: 1.5rem;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.6);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass morphism effect */
.glass-morphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Hero background pattern */
.hero-pattern {
  background-image:
    radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(147, 51, 234, 0.1) 0%, transparent 50%);
}

